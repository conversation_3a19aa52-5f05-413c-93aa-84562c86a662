#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动画模块
包含动画创建、帧更新等功能
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Circle

# 设置中文字体
plt.rcParams["font.sans-serif"] = ["SimHei", "Arial Unicode MS", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class AnimationCreator:
    """动画创建器类"""

    def __init__(self, simulation):
        """
        初始化动画创建器

        Parameters:
        simulation: HexagonSimulation - 仿真对象
        """
        self.sim = simulation

    def create_animation(
        self,
        save_gif=False,
        gif_filename="hexagon_simulation.gif",
        interval=50,
        show_trajectories=True,
    ):
        """
        创建动画（完全按照原版main.py布局）
        """
        print("创建动画...")

        fig, ax = plt.subplots(figsize=(12, 14))
        ax.set_aspect("equal")
        ax.grid(True, alpha=0.3)
        ax.set_title(
            f"六边形卷针薄膜包覆动态仿真\n旋转中心X偏移: {self.sim.rotation_center_x_offset:.1f} mm",
            fontsize=16,
            fontweight="bold",
        )
        ax.set_xlabel("X轴", fontsize=12)
        ax.set_ylabel("Y轴", fontsize=12)

        # 调整显示范围以包含两个过辊（与main.py一致）
        x_min = min(
            self.sim.B[0] - 10, -(abs(self.sim.original_vertices[:, 0]).max() + 10)
        )
        x_max = max(
            self.sim.A[0] + 10, (abs(self.sim.original_vertices[:, 0]).max() + 10)
        )
        ax.set_xlim(x_min, x_max)
        ax.set_ylim(
            -(abs(self.sim.original_vertices[:, 0]).max() + 10), self.sim.A[1] + 30
        )

        # 绘制过辊A圆心和圆
        ax.plot(
            self.sim.A[0],
            self.sim.A[1],
            "bo",
            markersize=15,
            markerfacecolor="blue",
            markeredgecolor="darkblue",
            markeredgewidth=2,
            zorder=10,
        )
        ax.text(
            self.sim.A[0] + 3,
            self.sim.A[1],
            "过辊A",
            fontsize=14,
            color="blue",
            fontweight="bold",
        )

        # 绘制过辊A圆
        roller_circle_A = plt.Circle(
            (self.sim.A[0], self.sim.A[1]),
            self.sim.roller_radius,
            fill=False,
            color="blue",
            linewidth=2,
            alpha=0.7,
            zorder=5,
        )
        ax.add_patch(roller_circle_A)

        # 绘制过辊B圆心和圆
        ax.plot(
            self.sim.B[0],
            self.sim.B[1],
            "ro",
            markersize=15,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            zorder=10,
        )
        ax.text(
            self.sim.B[0] - 10,
            self.sim.B[1],
            "过辊B",
            fontsize=14,
            color="red",
            fontweight="bold",
        )

        # 绘制过辊B圆
        roller_circle_B = plt.Circle(
            (self.sim.B[0], self.sim.B[1]),
            self.sim.roller_radius,
            fill=False,
            color="red",
            linewidth=2,
            alpha=0.7,
            zorder=5,
        )
        ax.add_patch(roller_circle_B)

        # 绘制薄膜走带路径（从过辊B到过辊A）
        B_exit = np.array([-30.0, 82.0])
        A_entry = np.array([0.5, 82.0])
        ax.plot(
            [B_exit[0], A_entry[0]],
            [B_exit[1], A_entry[1]],
            "k-",
            linewidth=3,
            alpha=0.8,
            zorder=3,
            label="薄膜走带",
        )
        ax.plot(B_exit[0], B_exit[1], "ko", markersize=8, zorder=8)
        ax.plot(A_entry[0], A_entry[1], "ko", markersize=8, zorder=8)

        # 绘制几何中心和旋转中心
        ax.plot(
            self.sim.geometric_center[0],
            self.sim.geometric_center[1],
            "gs",
            markersize=12,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            zorder=10,
            label="几何中心",
        )
        ax.plot(
            self.sim.rotation_center[0],
            self.sim.rotation_center[1],
            "r^",
            markersize=12,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            zorder=10,
            label="旋转中心",
        )

        # 绘制偏移线
        ax.plot(
            [self.sim.geometric_center[0], self.sim.rotation_center[0]],
            [self.sim.geometric_center[1], self.sim.rotation_center[1]],
            "k--",
            alpha=0.7,
            linewidth=2,
            zorder=5,
            label=f"X偏移: {self.sim.rotation_center_x_offset:.1f}mm",
        )

        # 初始化动画元素
        animation_elements = self._initialize_animation_elements(ax)

        # 为了动画流畅，每10帧显示一次（与main.py一致）
        frame_skip = 10
        frames = range(0, len(self.sim.theta), frame_skip)

        # 创建动画函数
        def animate(frame_idx):
            i = frame_idx * frame_skip
            if i >= len(self.sim.theta):
                return tuple(animation_elements.values())
            return self._update_frame(i, animation_elements)

        # 创建动画
        anim = animation.FuncAnimation(
            fig,
            animate,
            frames=len(frames),
            interval=interval,
            blit=True,
            repeat=True,
        )

        # 保存GIF（优先使用内存效率高的writer）
        if save_gif:
            print(f"正在保存动画为 {gif_filename}...")
            try:
                # 首先尝试使用FFmpeg writer（内存消耗最小）
                anim.save(gif_filename, writer="ffmpeg", fps=20)
                print("动画保存完成! (使用FFmpeg - 内存效率最高)")
            except Exception as e:
                print(f"FFmpeg保存失败: {e}")
                try:
                    # 尝试使用ImageMagick writer（内存消耗中等）
                    anim.save(gif_filename, writer="imagemagick", fps=20)
                    print("动画保存完成! (使用ImageMagick - 内存效率中等)")
                except Exception as e2:
                    print(f"ImageMagick保存失败: {e2}")
                    try:
                        # 最后使用Pillow writer（内存消耗最大，但兼容性最好）
                        print("使用Pillow保存 (内存消耗较大，请耐心等待)...")
                        anim.save(gif_filename, writer="pillow", fps=20)
                        print("动画保存完成! (使用Pillow - 兼容性最好)")
                    except Exception as e3:
                        print(f"Pillow保存失败: {e3}")
                        print("所有保存方法都失败了，请检查依赖安装")
                        print("建议安装: pip install imageio imageio-ffmpeg pillow")

        plt.show()
        return anim

    def _draw_fixed_elements(self, ax):
        """绘制固定元素"""
        # 绘制过辊A
        roller_circle = Circle(
            self.sim.A, self.sim.roller_radius, fill=False, color="red", linewidth=2
        )
        ax.add_patch(roller_circle)
        ax.plot(self.sim.A[0], self.sim.A[1], "r^", markersize=10, label="过辊A")

        # 绘制旋转中心
        ax.plot(
            self.sim.rotation_center[0],
            self.sim.rotation_center[1],
            "ko",
            markersize=8,
            label="旋转中心",
        )

        # 添加图例
        ax.legend(loc="upper right")

    def _initialize_animation_elements(self, ax):
        """初始化动画元素（按照main.py原版布局）"""
        elements = {}

        # 初始化动画元素（与main.py完全一致）
        # 原始卷针（始终显示，不考虑厚度影响）
        (elements["original_hex_line"],) = ax.plot(
            [], [], "b--", linewidth=2, alpha=0.5, label="原始卷针"
        )
        # 当前卷针（考虑厚度影响）
        (elements["current_hex_line"],) = ax.plot(
            [], [], "r-", linewidth=2, label="当前卷针"
        )
        (elements["film_line"],) = ax.plot([], [], "r-", linewidth=3, label="薄膜切线")
        (elements["contact_point"],) = ax.plot(
            [],
            [],
            "ro",
            markersize=10,
            markerfacecolor="red",
            markeredgecolor="darkred",
            markeredgewidth=2,
            label="接触点",
        )

        # 过辊接触点
        (elements["roller_contact_point"],) = ax.plot(
            [],
            [],
            "go",
            markersize=8,
            markerfacecolor="green",
            markeredgecolor="darkgreen",
            markeredgewidth=2,
            label="过辊接触点",
        )

        # 特殊点位
        (elements["upper_point"],) = ax.plot(
            [],
            [],
            "mo",
            markersize=10,
            markerfacecolor="magenta",
            markeredgecolor="darkmagenta",
            markeredgewidth=2,
            label="上方点",
        )
        (elements["lower_point"],) = ax.plot(
            [],
            [],
            "co",
            markersize=10,
            markerfacecolor="cyan",
            markeredgecolor="darkcyan",
            markeredgewidth=2,
            label="下方点",
        )

        # A点到上方点的连线
        (elements["connection_line"],) = ax.plot(
            [], [], "m--", linewidth=2, alpha=0.7, label="A-上方点连线"
        )

        # 轨迹线
        (elements["trajectory_line"],) = ax.plot(
            [], [], "r.", markersize=1, alpha=0.3, label="接触轨迹"
        )
        (elements["upper_trajectory"],) = ax.plot(
            [], [], "m.", markersize=1, alpha=0.3, label="上方点轨迹"
        )
        (elements["lower_trajectory"],) = ax.plot(
            [], [], "c.", markersize=1, alpha=0.3, label="下方点轨迹"
        )

        # 信息文本
        elements["info_text"] = ax.text(
            0.02,
            0.98,
            "",
            transform=ax.transAxes,
            verticalalignment="top",
            fontsize=12,
            fontweight="bold",
            bbox=dict(boxstyle="round", facecolor="yellow", alpha=0.8),
        )

        return elements

    def _update_frame(self, frame, elements):
        """更新动画帧（按照main.py原版逻辑）"""
        if frame >= len(self.sim.theta):
            return tuple(elements.values())

        # 计算当前层数和累积厚度
        layer_num, _ = self.sim.calculate_layer_from_angle(self.sim.theta_deg[frame])

        # 1. 更新原始六边形（不考虑厚度影响）- 蓝色虚线
        # 始终使用原始几何，不应用任何厚度影响
        original_rotated = self.sim.rotate_vertices_around_center_with_custom_vertices(
            self.sim.theta[frame], self.sim.original_vertices
        )

        if self.sim.use_rounded_corners and self.sim.rounded_corner_calc:
            # 圆角模式：使用原始几何的圆角轮廓
            # 确保使用厚度为0的圆角计算器状态
            self.sim.rounded_corner_calc.apply_thickness_impact(0.0)
            original_hex_x, original_hex_y = self._get_rounded_outline(
                original_rotated,
                self.sim.rounded_corner_calc,
                rotation_center=self.sim.rotation_center,
                theta=self.sim.theta[frame],
            )
        else:
            # 尖角模式：直接连接顶点
            original_hex_x = np.append(original_rotated[:, 0], original_rotated[0, 0])
            original_hex_y = np.append(original_rotated[:, 1], original_rotated[0, 1])

        elements["original_hex_line"].set_data(original_hex_x, original_hex_y)
        # 确保原始菱形显示为蓝色虚线
        elements["original_hex_line"].set_color("blue")
        elements["original_hex_line"].set_linestyle("--")
        elements["original_hex_line"].set_linewidth(2)

        # 2. 更新当前六边形（考虑厚度影响）- 修复厚度应用逻辑
        # 使用仿真中实际计算的累积厚度，而不是简化的层数计算
        actual_thickness = self.sim.accumulated_thickness[frame]

        if actual_thickness > 1e-6:  # 有实际厚度
            if self.sim.use_rounded_corners and self.sim.rounded_corner_calc:
                # 圆角模式：使用圆角计算器的厚度影响
                current_vertices, _ = (
                    self.sim.rounded_corner_calc.apply_thickness_impact(
                        actual_thickness
                    )
                )
            else:
                # 尖角模式：使用物理方法更新顶点
                current_vertices = self.sim.thickness_calc.update_vertices_physical(
                    actual_thickness
                )
        else:
            # 第一圈：使用原始几何，但为了区别显示，可以稍微调整显示效果
            current_vertices = self.sim.original_vertices.copy()

        current_rotated = self.sim.rotate_vertices_around_center_with_custom_vertices(
            self.sim.theta[frame], current_vertices
        )

        if self.sim.use_rounded_corners and self.sim.rounded_corner_calc:
            # 圆角模式：绘制圆角轮廓
            current_hex_x, current_hex_y = self._get_rounded_outline(
                current_rotated,
                self.sim.rounded_corner_calc,
                rotation_center=self.sim.rotation_center,
                theta=self.sim.theta[frame],
            )
        else:
            # 尖角模式：直接连接顶点
            current_hex_x = np.append(current_rotated[:, 0], current_rotated[0, 0])
            current_hex_y = np.append(current_rotated[:, 1], current_rotated[0, 1])
        elements["current_hex_line"].set_data(current_hex_x, current_hex_y)
        # 确保当前菱形显示为红色实线
        elements["current_hex_line"].set_color("red")
        elements["current_hex_line"].set_linestyle("-")
        elements["current_hex_line"].set_linewidth(2)

        # 更新接触点
        contact = self.sim.contact_points[frame]
        elements["contact_point"].set_data([contact[0]], [contact[1]])

        # 更新过辊接触点
        roller_contact = self.sim.roller_contact_points[frame]
        elements["roller_contact_point"].set_data(
            [roller_contact[0]], [roller_contact[1]]
        )

        # 更新薄膜切线 - 从接触点到过辊接触点（与main.py一致）
        # 绘制从接触点到过辊接触点的切线
        elements["film_line"].set_data(
            [contact[0], roller_contact[0]], [contact[1], roller_contact[1]]
        )
        elements["film_line"].set_color("green")
        elements["film_line"].set_linewidth(3)

        # 更新特殊点位（与main.py一致）
        elements["upper_point"].set_data(
            [self.sim.upper_point_trajectory[frame, 0]],
            [self.sim.upper_point_trajectory[frame, 1]],
        )
        elements["lower_point"].set_data(
            [self.sim.lower_point_trajectory[frame, 0]],
            [self.sim.lower_point_trajectory[frame, 1]],
        )

        # 更新A点到上方点的连线（与main.py一致）
        elements["connection_line"].set_data(
            [self.sim.A[0], self.sim.upper_point_trajectory[frame, 0]],
            [self.sim.A[1], self.sim.upper_point_trajectory[frame, 1]],
        )

        # 更新轨迹（与main.py一致）
        if frame > 0:
            elements["trajectory_line"].set_data(
                self.sim.contact_points[:frame:5, 0],
                self.sim.contact_points[:frame:5, 1],
            )
            elements["upper_trajectory"].set_data(
                self.sim.upper_point_trajectory[:frame:5, 0],
                self.sim.upper_point_trajectory[:frame:5, 1],
            )
            elements["lower_trajectory"].set_data(
                self.sim.lower_point_trajectory[:frame:5, 0],
                self.sim.lower_point_trajectory[:frame:5, 1],
            )

        # 更新信息文本（与main.py完全一致）
        angle_deg = self.sim.theta_deg[frame]
        current_L = self.sim.L[frame]
        current_S = self.sim.S[frame]
        current_S_total = self.sim.S_total[frame]
        contact_type = self.sim.contact_type[frame]
        upper_pos = self.sim.upper_point_trajectory[frame]
        lower_pos = self.sim.lower_point_trajectory[frame]
        tangent_length = self.sim.roller_contact_distances[frame]

        elements["info_text"].set_text(
            f"角度: {angle_deg:.1f}°\n"
            f"过辊到接触点: {current_L:.2f}\n"
            f"总薄膜长度: {current_S_total:.2f}\n"
            f"  切线部分: {tangent_length:.2f}\n"
            f"  包覆部分: {current_S:.2f}\n"
            f"接触类型: {contact_type}\n"
            f"上方点: ({upper_pos[0]:.1f}, {upper_pos[1]:.1f})\n"
            f"下方点: ({lower_pos[0]:.1f}, {lower_pos[1]:.1f})\n"
            f"X偏移: {self.sim.rotation_center_x_offset:.1f} mm"
        )

        return tuple(elements.values())

    def _get_rounded_outline(
        self, vertices, rounded_corner_calc, rotation_center=None, theta=0
    ):
        """
        获取圆角六边形的轮廓点（用于动画绘制）
        简化版本，生成足够的点来近似圆角形状

        Parameters:
        vertices: np.array - 顶点坐标（可能已旋转）
        rounded_corner_calc: RoundedCornerCalculator - 圆角计算器
        rotation_center: np.array - 旋转中心（如果顶点已旋转）
        theta: float - 旋转角度（弧度，如果顶点已旋转）
        """
        outline_x = []
        outline_y = []

        n = len(vertices)

        # 获取圆心坐标（如果顶点已旋转，圆心也需要旋转）
        if rotation_center is not None and theta != 0:
            # 顶点已旋转，需要旋转圆心
            rotated_centers = rounded_corner_calc.get_rotated_centers(
                rotation_center, theta
            )
        else:
            # 顶点未旋转，使用原始圆心
            rotated_centers = rounded_corner_calc.circle_centers

        # 预计算所有切点
        all_tangent_points = []
        for i in range(n):
            corner_info = rounded_corner_calc.get_rounded_corner_info(i)
            radius = corner_info["radius"]
            center = rotated_centers[i]  # 使用正确的圆心

            # 计算当前顶点的两个切点
            tangent_point1, tangent_point2 = (
                rounded_corner_calc.calculate_tangent_points_on_edges_rotated(
                    center, radius, i, vertices
                )
            )
            all_tangent_points.append((tangent_point1, tangent_point2))

        # 生成轮廓点
        for i in range(n):
            _, current_tangent2 = all_tangent_points[i]
            next_tangent1, _ = all_tangent_points[(i + 1) % n]

            # 添加从当前顶点第二个切点到下一个顶点第一个切点的直线段
            outline_x.extend([current_tangent2[0], next_tangent1[0]])
            outline_y.extend([current_tangent2[1], next_tangent1[1]])

            # 添加圆弧点（简化版本）
            corner_info = rounded_corner_calc.get_rounded_corner_info((i + 1) % n)
            center = rotated_centers[(i + 1) % n]  # 使用正确的圆心
            radius = corner_info["radius"]

            # 计算圆弧角度
            start_angle = np.arctan2(
                next_tangent1[1] - center[1], next_tangent1[0] - center[0]
            )
            next_tangent2 = all_tangent_points[(i + 1) % n][1]
            end_angle = np.arctan2(
                next_tangent2[1] - center[1], next_tangent2[0] - center[0]
            )

            # 确保角度在正确范围内
            start_angle = start_angle % (2 * np.pi)
            end_angle = end_angle % (2 * np.pi)

            if end_angle < start_angle:
                end_angle += 2 * np.pi

            # 限制角度差
            angle_diff = end_angle - start_angle
            if angle_diff > np.pi:
                start_angle, end_angle = end_angle - 2 * np.pi, start_angle

            # 生成圆弧点
            num_arc_points = max(3, int(abs(angle_diff) * 10 / np.pi))
            arc_angles = np.linspace(start_angle, end_angle, num_arc_points)

            for angle in arc_angles[1:]:  # 跳过第一个点（已经添加）
                arc_x = center[0] + radius * np.cos(angle)
                arc_y = center[1] + radius * np.sin(angle)
                outline_x.append(arc_x)
                outline_y.append(arc_y)

        # 闭合轮廓
        if outline_x and outline_y:
            outline_x.append(outline_x[0])
            outline_y.append(outline_y[0])

        return np.array(outline_x), np.array(outline_y)
