#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
厚度计算模块
包含物理厚度和几何厚度的计算方法
"""

import numpy as np
from geometry_utils import GeometryUtils


class ThicknessCalculator:
    """厚度计算器类"""

    def __init__(self, original_vertices, rounded_corner_calc=None):
        """
        初始化厚度计算器

        Parameters:
        original_vertices: np.array - 原始顶点坐标
        rounded_corner_calc: RoundedCornerCalculator - 圆角计算器（可选）
        """
        self.original_vertices = original_vertices
        self.rounded_corner_calc = rounded_corner_calc

    def update_vertices_physical(self, accumulated_thickness):
        """
        根据累积厚度更新顶点位置（物理真实方法）
        用于接触点计算：
        - 对于尖角（小于90度）：直接按厚度值向外扩展
        - 对于钝角（大于90度）：使用角平分线方法，在平分线上扩展厚度值

        Parameters:
        accumulated_thickness: float - 累积的隔膜厚度

        Returns:
        updated_vertices: np.array - 更新后的顶点坐标
        """
        if accumulated_thickness <= 0:
            return self.original_vertices.copy()

        updated_vertices = []
        n = len(self.original_vertices)

        for i in range(n):
            current_vertex = self.original_vertices[i]
            prev_vertex = self.original_vertices[(i - 1) % n]
            next_vertex = self.original_vertices[(i + 1) % n]

            # 计算顶点角度
            angle = GeometryUtils.calculate_vertex_angle(self.original_vertices, i)

            if angle < 90:
                # 尖角（小于90度）：直接按厚度值向外扩展
                # 计算几何中心
                center = np.mean(self.original_vertices, axis=0)
                # 从中心到顶点的向量
                to_vertex = current_vertex - center
                # 向量长度（距离）
                distance = np.linalg.norm(to_vertex)

                if distance > 1e-10:  # 避免除零
                    # 单位向量
                    unit_vector = to_vertex / distance
                    # 新的顶点位置：原位置 + 厚度方向的扩展
                    new_vertex = current_vertex + unit_vector * accumulated_thickness
                else:
                    new_vertex = current_vertex
            else:
                # 钝角（大于等于90度）：使用角平分线方法
                # 计算两个边的单位向量
                edge1 = prev_vertex - current_vertex
                edge2 = next_vertex - current_vertex

                edge1_unit = edge1 / np.linalg.norm(edge1)
                edge2_unit = edge2 / np.linalg.norm(edge2)

                # 计算角平分线方向（向外）
                bisector = edge1_unit + edge2_unit
                if np.linalg.norm(bisector) > 1e-10:
                    bisector_unit = -bisector / np.linalg.norm(
                        bisector
                    )  # 取反得到向外方向
                else:
                    # 如果角平分线为零向量（180度角），使用垂直方向
                    edge_vec = next_vertex - prev_vertex
                    bisector_unit = np.array([-edge_vec[1], edge_vec[0]])
                    bisector_unit = bisector_unit / np.linalg.norm(bisector_unit)

                # 在平分线上扩展厚度值
                new_vertex = current_vertex + accumulated_thickness * bisector_unit

            updated_vertices.append(new_vertex)

        return np.array(updated_vertices)

    def update_vertices_geometric(self, accumulated_thickness):
        """
        根据累积厚度更新顶点位置（几何精确方法）
        用于周长和包覆长度计算：每个顶点沿角平分线向外移动
        移动距离 = 厚度 / sin(角度/2)

        Parameters:
        accumulated_thickness: float - 累积的隔膜厚度

        Returns:
        updated_vertices: np.array - 更新后的顶点坐标
        """
        if accumulated_thickness <= 0:
            return self.original_vertices.copy()

        updated_vertices = []
        n = len(self.original_vertices)

        for i in range(n):
            current_vertex = self.original_vertices[i]
            prev_vertex = self.original_vertices[(i - 1) % n]
            next_vertex = self.original_vertices[(i + 1) % n]

            # 计算顶点角度
            angle = GeometryUtils.calculate_vertex_angle(self.original_vertices, i)

            # 计算两个边的单位向量
            edge1 = prev_vertex - current_vertex
            edge2 = next_vertex - current_vertex

            edge1_unit = edge1 / np.linalg.norm(edge1)
            edge2_unit = edge2 / np.linalg.norm(edge2)

            # 计算角平分线方向（向外）
            # 内角平分线向内，我们需要外角平分线向外
            bisector = edge1_unit + edge2_unit
            if np.linalg.norm(bisector) > 1e-10:
                bisector_unit = -bisector / np.linalg.norm(bisector)  # 取反得到向外方向
            else:
                # 如果角平分线为零向量（180度角），使用垂直方向
                edge_vec = next_vertex - prev_vertex
                bisector_unit = np.array([-edge_vec[1], edge_vec[0]])
                bisector_unit = bisector_unit / np.linalg.norm(bisector_unit)

            # 计算移动距离 = 厚度 / sin(角度/2)
            half_angle_rad = np.radians(angle / 2)
            if np.sin(half_angle_rad) > 1e-10:
                move_distance = accumulated_thickness / np.sin(half_angle_rad)
            else:
                # 如果角度接近0或180度，使用简单的径向扩展
                center = np.mean(self.original_vertices, axis=0)
                to_vertex = current_vertex - center
                if np.linalg.norm(to_vertex) > 1e-10:
                    bisector_unit = to_vertex / np.linalg.norm(to_vertex)
                    move_distance = accumulated_thickness
                else:
                    move_distance = 0

            # 新顶点位置
            new_vertex = current_vertex + move_distance * bisector_unit
            updated_vertices.append(new_vertex)

        return np.array(updated_vertices)

    def calculate_layer_consumption(self, layer_number, film_thickness):
        """
        计算每层的隔膜消耗长度
        使用几何精确方法

        Parameters:
        layer_number: int - 层数（从0开始）
        film_thickness: float - 单层隔膜厚度

        Returns:
        consumption: float - 该层的隔膜消耗长度
        """
        if layer_number <= 0:
            # 第0层：根据模式选择正确的周长计算方法
            if self.rounded_corner_calc is not None:
                # 圆角模式：使用圆角周长计算（厚度为0）
                return self.calculate_rounded_perimeter(0.0)
            else:
                # 尖角模式：使用原始顶点周长
                return GeometryUtils.calculate_perimeter(self.original_vertices)

        # 计算该层的累积厚度
        accumulated_thickness = layer_number * film_thickness

        # 使用几何精确方法计算新的顶点位置
        updated_vertices = self.update_vertices_geometric(accumulated_thickness)

        # 计算新的周长
        if self.rounded_corner_calc is not None:
            # 圆角模式：使用圆角周长计算
            return self.calculate_rounded_perimeter(accumulated_thickness)
        else:
            # 尖角模式：使用几何周长计算
            return GeometryUtils.calculate_perimeter(updated_vertices)

    def update_vertices_rounded_mode(self, accumulated_thickness):
        """
        圆角模式下的顶点更新
        使用圆角计算器的厚度影响逻辑

        Parameters:
        accumulated_thickness: float - 累积的隔膜厚度

        Returns:
        updated_vertices: np.array - 更新后的顶点坐标
        """
        if self.rounded_corner_calc is None:
            # 如果没有圆角计算器，回退到物理方法
            return self.update_vertices_physical(accumulated_thickness)

        # 使用圆角计算器的厚度影响方法
        updated_vertices, _ = self.rounded_corner_calc.apply_thickness_impact(
            accumulated_thickness
        )
        return updated_vertices

    def calculate_rounded_perimeter(self, accumulated_thickness):
        """
        计算圆角模式下的周长
        包括圆弧长度和边长

        Parameters:
        accumulated_thickness: float - 累积的隔膜厚度

        Returns:
        total_perimeter: float - 总周长
        """
        if self.rounded_corner_calc is None:
            # 如果没有圆角计算器，使用几何方法
            updated_vertices = self.update_vertices_geometric(accumulated_thickness)
            return GeometryUtils.calculate_perimeter(updated_vertices)

        # 应用厚度影响
        updated_vertices, updated_centers = (
            self.rounded_corner_calc.apply_thickness_impact(accumulated_thickness)
        )

        total_perimeter = 0.0
        n = len(updated_vertices)

        # 计算每个顶点的圆弧长度和连接边长
        for i in range(n):
            # 圆弧长度
            radius = (
                self.rounded_corner_calc.sharp_radius
                if i in [0, 3]
                else self.rounded_corner_calc.blunt_radius
            )

            arc_length = self.rounded_corner_calc.calculate_arc_length_between_tangents(
                updated_centers[i], radius, i, updated_vertices
            )
            total_perimeter += arc_length

            # 连接边长（从当前顶点的圆弧结束点到下一个顶点的圆弧起始点）
            current_tangent1, current_tangent2 = (
                self.rounded_corner_calc.calculate_tangent_points_on_edges_rotated(
                    updated_centers[i], radius, i, updated_vertices
                )
            )

            next_i = (i + 1) % n
            next_radius = (
                self.rounded_corner_calc.sharp_radius
                if next_i in [0, 3]
                else self.rounded_corner_calc.blunt_radius
            )
            next_tangent1, _ = (
                self.rounded_corner_calc.calculate_tangent_points_on_edges_rotated(
                    updated_centers[next_i], next_radius, next_i, updated_vertices
                )
            )

            edge_length = np.linalg.norm(next_tangent1 - current_tangent2)
            total_perimeter += edge_length

        return total_perimeter
