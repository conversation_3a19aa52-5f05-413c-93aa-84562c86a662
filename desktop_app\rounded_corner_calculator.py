#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
圆角计算器模块
处理菱形尖角修改为圆角的相关计算
"""

import numpy as np
from geometry_utils import GeometryUtils


class RoundedCornerCalculator:
    """圆角计算器类"""

    def __init__(self, original_vertices, sharp_radius=0.8, blunt_radius=12.0):
        """
        初始化圆角计算器

        Parameters:
        original_vertices: np.array - 原始顶点坐标
        sharp_radius: float - 锐角圆角半径
        blunt_radius: float - 钝角圆角半径
        """
        self.original_vertices = original_vertices
        self.original_sharp_radius = sharp_radius
        self.original_blunt_radius = blunt_radius
        self.sharp_radius = sharp_radius
        self.blunt_radius = blunt_radius

        # 计算顶点角度
        self.vertex_angles = []
        for i in range(len(original_vertices)):
            angle = self.calculate_vertex_angle(original_vertices, i)
            self.vertex_angles.append(angle)

        # 计算内切圆圆心
        self.circle_centers = self.calculate_incircle_centers()

        # 当前厚度
        self.current_thickness = 0.0

    def calculate_vertex_angle(self, vertices, vertex_index):
        """计算顶点的内角"""
        n = len(vertices)
        prev_vertex = vertices[(vertex_index - 1) % n]
        current_vertex = vertices[vertex_index]
        next_vertex = vertices[(vertex_index + 1) % n]

        # 计算两个边向量
        vec1 = prev_vertex - current_vertex
        vec2 = next_vertex - current_vertex

        # 计算角度
        dot_product = np.dot(vec1, vec2)
        norms = np.linalg.norm(vec1) * np.linalg.norm(vec2)

        if norms == 0:
            return 0

        cos_angle = np.clip(dot_product / norms, -1, 1)
        angle_rad = np.arccos(cos_angle)
        angle_deg = np.rad2deg(angle_rad)

        return angle_deg

    def calculate_incircle_center(self, vertices, vertex_index, radius):
        """计算单个顶点的内切圆圆心"""
        n = len(vertices)
        prev_vertex = vertices[(vertex_index - 1) % n]
        current_vertex = vertices[vertex_index]
        next_vertex = vertices[(vertex_index + 1) % n]

        # 计算两个边向量
        vec1 = prev_vertex - current_vertex
        vec2 = next_vertex - current_vertex

        # 归一化边向量
        vec1_norm = vec1 / np.linalg.norm(vec1)
        vec2_norm = vec2 / np.linalg.norm(vec2)

        # 计算角平分线方向
        bisector = vec1_norm + vec2_norm
        bisector_norm = bisector / np.linalg.norm(bisector)

        # 计算内角的一半
        angle = self.calculate_vertex_angle(vertices, vertex_index)
        half_angle_rad = np.deg2rad(angle / 2)

        # 计算圆心到顶点的距离
        distance = radius / np.sin(half_angle_rad)

        # 计算圆心位置
        center = current_vertex + bisector_norm * distance

        return center

    def calculate_incircle_centers(self):
        """根据菱形顶点和内切圆半径自动计算所有圆心"""
        centers = []

        for i in range(len(self.original_vertices)):
            # 计算顶点角度
            angle = self.vertex_angles[i]

            # 根据角度大小选择半径（锐角<90度，钝角>90度）
            if angle < 90:
                radius = self.sharp_radius
                _ = "锐角"
            else:
                radius = self.blunt_radius
                _ = "钝角"

            # 计算圆心
            center = self.calculate_incircle_center(self.original_vertices, i, radius)
            centers.append(center)

        return np.array(centers)

    def calculate_tangent_points_on_edges(self, center, radius, vertex_index, vertices):
        """计算内切圆在两条边上的切点"""
        n = len(vertices)
        current_vertex = vertices[vertex_index]
        prev_vertex = vertices[(vertex_index - 1) % n]
        next_vertex = vertices[(vertex_index + 1) % n]

        # 计算两条边上的切点
        tangent_point1 = self.calculate_tangent_point_on_line(
            center, radius, prev_vertex, current_vertex
        )
        tangent_point2 = self.calculate_tangent_point_on_line(
            center, radius, current_vertex, next_vertex
        )

        return tangent_point1, tangent_point2

    def calculate_tangent_point_on_line(self, center, radius, point1, point2):
        """
        计算内切圆与直线的切点

        对于内切圆，切点就是圆心到直线的垂足。
        这是因为内切圆与边相切，切点处圆心到边的距离等于半径，
        且圆心到边的最短距离就是垂直距离。

        Parameters:
        center: np.array - 圆心坐标
        radius: float - 圆半径（保留参数以保持接口一致性）
        point1, point2: np.array - 直线上的两个点

        Returns:
        tangent_point: np.array - 切点坐标（圆心到直线的垂足）
        """
        # 直线方向向量
        line_vec = point2 - point1
        line_length = np.linalg.norm(line_vec)

        if line_length < 1e-10:
            return point1

        # 单位方向向量
        line_unit = line_vec / line_length

        # 从point1到圆心的向量
        to_center = center - point1

        # 计算投影长度（圆心在直线上的投影）
        proj_length = np.dot(to_center, line_unit)

        # 投影点就是切点（圆心到直线的垂足）
        tangent_point = point1 + proj_length * line_unit

        return tangent_point

    def calculate_arc_length_between_tangents(
        self, center, radius, vertex_index, vertices
    ):
        """计算两个切点之间的圆弧长度"""
        tangent_point1, tangent_point2 = self.calculate_tangent_points_on_edges_rotated(
            center, radius, vertex_index, vertices
        )

        # 计算切点相对于圆心的角度
        to_tangent1 = tangent_point1 - center
        to_tangent2 = tangent_point2 - center

        angle1 = np.arctan2(to_tangent1[1], to_tangent1[0])
        angle2 = np.arctan2(to_tangent2[1], to_tangent2[0])

        # 确保角度在[0, 2π]范围内
        angle1 = angle1 % (2 * np.pi)
        angle2 = angle2 % (2 * np.pi)

        # 计算角度差（取较小的角度）
        angle_diff = abs(angle2 - angle1)
        if angle_diff > np.pi:
            angle_diff = 2 * np.pi - angle_diff

        # 圆弧长度 = 半径 × 角度（弧度）
        arc_length = radius * angle_diff

        return arc_length

    def calculate_total_perimeter_with_rounded_corners(self, vertices=None):
        """计算带圆角的总周长"""
        if vertices is None:
            vertices = self.original_vertices

        total_perimeter = 0.0
        n = len(vertices)

        for i in range(n):
            # 计算边长（减去两端的圆角切点距离）
            current_vertex = vertices[i]
            next_vertex = vertices[(i + 1) % n]
            edge_length = np.linalg.norm(next_vertex - current_vertex)

            # 减去当前顶点和下一个顶点的圆角影响
            current_radius = (
                self.sharp_radius if self.vertex_angles[i] < 90 else self.blunt_radius
            )
            next_radius = (
                self.sharp_radius
                if self.vertex_angles[(i + 1) % n] < 90
                else self.blunt_radius
            )

            # 计算切点到顶点的距离
            current_angle = self.vertex_angles[i]
            next_angle = self.vertex_angles[(i + 1) % n]

            current_tangent_distance = current_radius / np.tan(
                np.deg2rad(current_angle / 2)
            )
            next_tangent_distance = next_radius / np.tan(np.deg2rad(next_angle / 2))

            # 实际边长 = 原边长 - 两端切点距离
            actual_edge_length = (
                edge_length - current_tangent_distance - next_tangent_distance
            )
            total_perimeter += actual_edge_length

            # 添加圆弧长度
            arc_length = self.calculate_arc_length_between_tangents(
                self.circle_centers[i], current_radius, i, vertices
            )
            total_perimeter += arc_length

        return total_perimeter

    def update_radii_with_thickness(self, thickness):
        """根据厚度更新圆角半径"""
        new_sharp_radius = self.original_sharp_radius + thickness
        new_blunt_radius = self.original_blunt_radius + thickness
        return new_sharp_radius, new_blunt_radius

    def apply_thickness_impact(self, thickness):
        """
        应用厚度影响，更新顶点和内切圆参数
        基于main_coordinate_tangent_analysis.py的逻辑

        Parameters:
        thickness: float - 累积厚度

        Returns:
        updated_vertices: np.array - 更新后的顶点
        updated_centers: np.array - 更新后的圆心
        """
        self.current_thickness = thickness

        if thickness <= 0:
            # 厚度为0：使用原始几何
            self.sharp_radius = self.original_sharp_radius
            self.blunt_radius = self.original_blunt_radius
            # 重新计算原始圆心（确保一致性）
            self.circle_centers = self.calculate_incircle_centers()
            return self.original_vertices.copy(), self.circle_centers.copy()

        # 厚度大于0：应用厚度影响
        # 1. 更新内切圆半径
        self.sharp_radius = self.original_sharp_radius + thickness
        self.blunt_radius = self.original_blunt_radius + thickness

        # 2. 计算向外平移后的顶点
        updated_vertices = self.calculate_outward_shifted_vertices(thickness)

        # 3. 基于新顶点和新半径计算新的内切圆圆心
        updated_centers = self.calculate_new_incircle_centers(updated_vertices)
        self.circle_centers = updated_centers

        return updated_vertices, updated_centers

    def calculate_outward_shifted_vertices(self, thickness):
        """计算向外平移后的顶点"""
        shifted_vertices = []
        n = len(self.original_vertices)

        for i in range(n):
            current_vertex = self.original_vertices[i]
            prev_vertex = self.original_vertices[(i - 1) % n]
            next_vertex = self.original_vertices[(i + 1) % n]

            # 计算两条边的单位法向量（向外）
            edge1 = current_vertex - prev_vertex
            edge2 = next_vertex - current_vertex

            # 边的法向量（向外）
            normal1 = np.array([-edge1[1], edge1[0]])
            normal1 = normal1 / np.linalg.norm(normal1)

            normal2 = np.array([-edge2[1], edge2[0]])
            normal2 = normal2 / np.linalg.norm(normal2)

            # 检查法向量方向是否向外（应该远离菱形中心）
            center = np.mean(self.original_vertices, axis=0)
            to_center1 = center - current_vertex
            to_center2 = center - current_vertex

            if np.dot(normal1, to_center1) > 0:
                normal1 = -normal1
            if np.dot(normal2, to_center2) > 0:
                normal2 = -normal2

            # 角平分线方向（向外）
            bisector = normal1 + normal2
            bisector_unit = bisector / np.linalg.norm(bisector)

            # 计算顶点角度
            cos_angle = np.dot(
                -edge1 / np.linalg.norm(edge1), edge2 / np.linalg.norm(edge2)
            )
            angle = np.arccos(np.clip(cos_angle, -1, 1))

            # 顶点向外移动的距离 = thickness / sin(angle/2)
            shift_distance = thickness / np.sin(angle / 2)

            # 新顶点位置
            new_vertex = current_vertex + shift_distance * bisector_unit
            shifted_vertices.append(new_vertex)

        return np.array(shifted_vertices)

    def calculate_new_incircle_centers(self, new_vertices):
        """基于新的顶点计算内切圆圆心"""
        new_centers = []

        for i in range(len(new_vertices)):
            n = len(new_vertices)
            prev_vertex = new_vertices[(i - 1) % n]
            current_vertex = new_vertices[i]
            next_vertex = new_vertices[(i + 1) % n]

            # 计算两个边的单位向量
            edge1 = prev_vertex - current_vertex
            edge2 = next_vertex - current_vertex

            edge1_length = np.linalg.norm(edge1)
            edge2_length = np.linalg.norm(edge2)

            if edge1_length == 0 or edge2_length == 0:
                # 退化情况，使用原始圆心
                new_centers.append(self.circle_centers[i])
                continue

            edge1_unit = edge1 / edge1_length
            edge2_unit = edge2 / edge2_length

            # 计算角平分线方向（向内）
            bisector = edge1_unit + edge2_unit
            bisector_length = np.linalg.norm(bisector)

            if bisector_length == 0:
                # 平行边的情况，使用垂直方向
                perpendicular = np.array([-edge1_unit[1], edge1_unit[0]])
                bisector_unit = perpendicular
            else:
                bisector_unit = bisector / bisector_length

            # 计算顶点角度
            cos_angle = np.dot(edge1_unit, edge2_unit)
            cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
            angle = np.arccos(cos_angle)

            if angle == 0:
                # 角度为0的退化情况
                new_centers.append(self.circle_centers[i])
                continue

            # 选择半径
            radius = self.sharp_radius if i in [0, 3] else self.blunt_radius

            # 圆心到顶点的距离 = 半径 / sin(角度/2)
            sin_half_angle = np.sin(angle / 2)
            if sin_half_angle == 0:
                # sin为0的退化情况
                new_centers.append(self.circle_centers[i])
                continue

            distance_to_vertex = radius / sin_half_angle

            # 新圆心位置
            new_center = current_vertex + distance_to_vertex * bisector_unit
            new_centers.append(new_center)

        return np.array(new_centers)

    def calculate_cross_tangent_with_constraint(
        self, roller_center, roller_radius, circle_center, circle_radius
    ):
        """
        计算两圆的公切线，约束条件：切点在过辊A上x>0.5
        基于main_coordinate_tangent_analysis.py的逻辑
        """
        import math

        x0, y0 = roller_center[0], roller_center[1]
        x1, y1 = circle_center[0], circle_center[1]
        R, r = roller_radius, circle_radius

        d = math.sqrt((x1 - x0) ** 2 + (y1 - y0) ** 2)

        if d <= abs(R - r) or d < R + r:
            return None

        _ = d * R / (R + r)
        cos_alpha = (R + r) / d
        alpha = math.acos(cos_alpha)

        Hpq = math.atan2(y1 - y0, x1 - x0)
        Hpa = Hpq + alpha
        Hpb = Hpq - alpha

        # 计算两条切线的切点
        tangents = []

        # 第一条切线
        x2 = x0 + R * math.cos(Hpa)
        y2 = y0 + R * math.sin(Hpa)
        point_A1 = np.array([x2, y2])

        x3 = x1 + r * math.cos(Hpa + math.pi)
        y3 = y1 + r * math.sin(Hpa + math.pi)
        point_D1 = np.array([x3, y3])

        if point_A1[0] > 0.5:  # 检查x>0.5约束
            length1 = np.linalg.norm(point_D1 - point_A1)
            tangents.append(
                {
                    "roller_point": point_A1,
                    "diamond_point": point_D1,
                    "length": length1,
                    "angle": Hpa,
                }
            )

        # 第二条切线
        x4 = x0 + R * math.cos(Hpb)
        y4 = y0 + R * math.sin(Hpb)
        point_A2 = np.array([x4, y4])

        x5 = x1 + r * math.cos(Hpb + math.pi)
        y5 = y1 + r * math.sin(Hpb + math.pi)
        point_D2 = np.array([x5, y5])

        if point_A2[0] > 0.5:  # 检查x>0.5约束
            length2 = np.linalg.norm(point_D2 - point_A2)
            tangents.append(
                {
                    "roller_point": point_A2,
                    "diamond_point": point_D2,
                    "length": length2,
                    "angle": Hpb,
                }
            )

        if not tangents:
            return None

        return {
            "valid_tangents": tangents,
            "selected": tangents[0],  # 选择第一个有效切线
            "all_calculated": [
                {"roller": point_A1, "diamond": point_D1, "valid": point_A1[0] > 0.5},
                {"roller": point_A2, "diamond": point_D2, "valid": point_A2[0] > 0.5},
            ],
        }

    def get_rounded_corner_info(self, vertex_index):
        """获取指定顶点的圆角信息"""
        angle = self.vertex_angles[vertex_index]
        radius = self.sharp_radius if angle < 90 else self.blunt_radius
        center = self.circle_centers[vertex_index]

        return {
            "vertex_index": vertex_index,
            "angle": angle,
            "radius": radius,
            "center": center,
            "is_sharp": angle < 90,
        }

    def get_rotated_centers(self, rotation_center, theta):
        """
        获取旋转后的圆心坐标

        Parameters:
        rotation_center: np.array - 旋转中心
        theta: float - 旋转角度（弧度）

        Returns:
        rotated_centers: np.array - 旋转后的圆心坐标
        """

        rotated_centers = GeometryUtils.rotate_points_around_center(
            self.circle_centers, rotation_center, theta
        )
        return rotated_centers

    def calculate_tangent_points_on_edges_rotated(
        self, center, radius, vertex_index, rotated_vertices
    ):
        """
        计算旋转后顶点的内切圆在两条边上的切点

        Parameters:
        center: np.array - 旋转后的圆心
        radius: float - 圆半径
        vertex_index: int - 顶点索引
        rotated_vertices: np.array - 旋转后的顶点坐标

        Returns:
        tangent_point1, tangent_point2: np.array - 两个切点
        """
        n = len(rotated_vertices)
        current_vertex = rotated_vertices[vertex_index]
        prev_vertex = rotated_vertices[(vertex_index - 1) % n]
        next_vertex = rotated_vertices[(vertex_index + 1) % n]

        # 计算两条边上的切点
        tangent_point1 = self.calculate_tangent_point_on_line(
            center, radius, prev_vertex, current_vertex
        )
        tangent_point2 = self.calculate_tangent_point_on_line(
            center, radius, current_vertex, next_vertex
        )

        return tangent_point1, tangent_point2

    def calculate_arc_length_to_tangent_point(
        self,
        circle_center,
        circle_radius,
        vertex_index,
        rotated_vertices,
        tangent_point,
    ):
        """
        计算从圆弧起点到公切线切点的圆弧长度
        基于main_coordinate_tangent_analysis.py的逻辑

        Parameters:
        circle_center: np.array - 内切圆圆心
        circle_radius: float - 内切圆半径
        vertex_index: int - 顶点索引
        rotated_vertices: np.array - 旋转后的顶点坐标
        tangent_point: np.array - 公切线在内切圆上的切点

        Returns:
        arc_length: float - 圆弧长度
        """
        # 计算圆弧的起点和终点
        start_angle, end_angle, arc_start_point, arc_end_point = (
            self.calculate_useful_arc_angles(
                circle_center, circle_radius, vertex_index, rotated_vertices
            )
        )

        # 计算公切线切点相对于圆心的角度
        to_tangent = tangent_point - circle_center
        tangent_angle = np.arctan2(to_tangent[1], to_tangent[0])
        tangent_angle = tangent_angle % (2 * np.pi)

        # 确保切点角度在圆弧范围内
        if end_angle < start_angle:
            end_angle += 2 * np.pi

        if tangent_angle < start_angle:
            tangent_angle += 2 * np.pi

        # 计算从圆弧起点到切点的角度差
        if start_angle <= tangent_angle <= end_angle:
            # 切点在圆弧范围内
            angle_diff = end_angle - tangent_angle
        else:
            # 切点不在圆弧范围内，使用最近的端点
            diff_to_start = abs(tangent_angle - start_angle)
            diff_to_end = abs(tangent_angle - end_angle)
            if diff_to_start < diff_to_end:
                angle_diff = 0  # 使用起点
            else:
                angle_diff = end_angle - start_angle  # 使用终点

        # 圆弧长度 = 半径 × 角度（弧度）
        arc_length = circle_radius * angle_diff

        return arc_length

    def calculate_useful_arc_angles(
        self, center, radius, vertex_index, rotated_vertices
    ):
        """
        基于实际切点计算有用圆弧的起始和结束角度
        """
        # 计算两条边上的切点
        tangent_point1, tangent_point2 = self.calculate_tangent_points_on_edges_rotated(
            center, radius, vertex_index, rotated_vertices
        )

        # 计算切点相对于圆心的角度
        to_tangent1 = tangent_point1 - center
        to_tangent2 = tangent_point2 - center

        angle1 = np.arctan2(to_tangent1[1], to_tangent1[0])
        angle2 = np.arctan2(to_tangent2[1], to_tangent2[0])

        # 确保角度在[0, 2π]范围内
        angle1 = angle1 % (2 * np.pi)
        angle2 = angle2 % (2 * np.pi)

        # 确定起始和结束角度（按逆时针方向）
        if angle2 < angle1:
            angle2 += 2 * np.pi

        return angle1, angle2, tangent_point1, tangent_point2
