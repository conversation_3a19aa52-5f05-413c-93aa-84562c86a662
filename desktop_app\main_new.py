#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六边形卷针隔膜包覆仿真 - 主程序
重构后的简化版本 - 增强版（包含数据保存和日志功能）
"""

import numpy as np
import pandas as pd
import os
import json
import logging
from datetime import datetime
from hexagon_simulation import HexagonSimulation


def setup_logging(output_dir="simulation_output"):
    """设置日志系统"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(output_dir, f"simulation_log_{timestamp}.log")

    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file, encoding="utf-8"),
            logging.StreamHandler(),  # 同时输出到控制台
        ],
    )

    logger = logging.getLogger(__name__)
    logger.info("=" * 60)
    logger.info("六边形卷针隔膜包覆仿真开始")
    logger.info("=" * 60)

    return logger, output_dir, timestamp


def save_simulation_config(config, output_dir, timestamp):
    """保存仿真配置参数"""
    config_file = os.path.join(output_dir, f"simulation_config_{timestamp}.json")

    # 转换numpy数组为列表以便JSON序列化
    json_config = {}
    for key, value in config.items():
        if isinstance(value, np.ndarray):
            json_config[key] = value.tolist()
        elif isinstance(value, np.floating):
            json_config[key] = float(value)
        elif isinstance(value, np.integer):
            json_config[key] = int(value)
        else:
            json_config[key] = value

    with open(config_file, "w", encoding="utf-8") as f:
        json.dump(json_config, f, indent=2, ensure_ascii=False)

    logging.info(f"配置参数已保存到: {config_file}")
    return config_file


def save_simulation_data_to_csv(sim, output_dir, timestamp):
    """保存仿真数据到CSV文件"""
    try:
        # 主要仿真数据
        main_data = {
            "angle_deg": sim.theta_deg,
            "angle_rad": sim.theta,
            "tangent_length_L": sim.L,
            "wrapping_length_S": sim.S,
            "total_length_S_total": sim.S_total,
            "layer_number": sim.layer_numbers,
            "accumulated_thickness": sim.accumulated_thickness,
            "layer_consumption": sim.layer_consumption,
            "contact_point_x": sim.contact_points[:, 0],
            "contact_point_y": sim.contact_points[:, 1],
            "contact_type": sim.contact_type,
            "roller_contact_x": sim.roller_contact_points[:, 0],
            "roller_contact_y": sim.roller_contact_points[:, 1],
            "roller_contact_distance": sim.roller_contact_distances,
            "upper_trajectory_x": sim.upper_point_trajectory[:, 0],
            "upper_trajectory_y": sim.upper_point_trajectory[:, 1],
            "lower_trajectory_x": sim.lower_point_trajectory[:, 0],
            "lower_trajectory_y": sim.lower_point_trajectory[:, 1],
        }

        # 添加详细分析数据（如果存在）
        if hasattr(sim, "tangent_A_lengths"):
            main_data.update(
                {
                    "tangent_A_length": sim.tangent_A_lengths,
                    "tangent_AB_length": sim.tangent_AB_lengths,
                    "arc_A_length": sim.arc_A_lengths,
                    "arc_B_length": sim.arc_B_lengths,
                    "contact_A_x": sim.contact_A_points[:, 0],
                    "contact_A_y": sim.contact_A_points[:, 1],
                    "contact_B_x": sim.contact_B_points[:, 0],
                    "contact_B_y": sim.contact_B_points[:, 1],
                    "contact_A_entry_x": sim.contact_A_entry_points[:, 0],
                    "contact_A_entry_y": sim.contact_A_entry_points[:, 1],
                }
            )

        # 创建DataFrame并保存
        df_main = pd.DataFrame(main_data)
        main_csv_file = os.path.join(output_dir, f"simulation_data_{timestamp}")

        encoding = "utf-8"

        df_main.to_csv(main_csv_file, index=False, encoding=encoding)
        logging.info(f"主要仿真数据已保存到: {main_csv_file} (编码: {encoding})")

        # 保存几何参数数据
        geometry_data = {
            "vertex_name": ["V1", "V2", "V3", "V4", "V5", "V6"],
            "original_x": sim.original_vertices[:, 0],
            "original_y": sim.original_vertices[:, 1],
        }

        df_geometry = pd.DataFrame(geometry_data)
        geometry_csv_file = os.path.join(output_dir, f"geometry_data_{timestamp}")

        df_geometry.to_csv(geometry_csv_file, index=False, encoding=encoding)
        logging.info(f"几何数据已保存到: {geometry_csv_file} (编码: {encoding})")

        # shutil.move(geometry_csv_file, f'{geometry_csv_file}.csv')
        # shutil.move(main_csv_file, f'{main_csv_file}.csv')

        return main_csv_file, geometry_csv_file

    except Exception as e:
        logging.error(f"数据保存过程中发生错误: {e}")
        # 返回空文件名，表示保存失败
        return "", ""


def save_simulation_summary(sim, output_dir, timestamp, config):
    """保存仿真结果摘要"""
    summary = {
        "simulation_info": {
            "timestamp": timestamp,
            "total_rotation_deg": sim.total_rotation,
            "step_angle_deg": sim.step_angle,
            "data_points": len(sim.theta_deg),
            "film_thickness_mm": sim.film_thickness,
            "use_rounded_corners": sim.use_rounded_corners,
        },
        "geometry_parameters": {
            "rotation_center_x_offset_mm": sim.rotation_center_x_offset,
            "geometric_center": sim.geometric_center.tolist(),
            "rotation_center": sim.rotation_center.tolist(),
            "roller_A_position": sim.A.tolist(),
            "roller_B_position": sim.B.tolist(),
            "roller_radius_mm": sim.roller_radius,
            "upper_point": sim.upper_point.tolist(),
            "lower_point": sim.lower_point.tolist(),
        },
        "simulation_results": {
            "final_layer_number": int(sim.layer_numbers[-1]),
            "max_accumulated_thickness_mm": float(sim.accumulated_thickness[-1]),
            "final_tangent_length_mm": float(sim.L[-1]),
            "final_wrapping_length_mm": float(sim.S[-1]),
            "total_film_consumption_mm": float(sim.S_total[-1]),
            "average_layer_consumption_mm": float(np.mean(sim.layer_consumption)),
            "tangent_length_range": {
                "min_mm": float(np.min(sim.L)),
                "max_mm": float(np.max(sim.L)),
                "variation_mm": float(np.max(sim.L) - np.min(sim.L)),
            },
            "wrapping_length_range": {
                "min_mm": float(np.min(sim.S)),
                "max_mm": float(np.max(sim.S)),
                "variation_mm": float(np.max(sim.S) - np.min(sim.S)),
            },
        },
    }

    # 添加圆角参数（如果启用）
    if sim.use_rounded_corners:
        summary["geometry_parameters"].update(
            {
                "sharp_radius_mm": sim.sharp_radius,
                "blunt_radius_mm": sim.blunt_radius,
            }
        )

    summary_file = os.path.join(output_dir, f"simulation_summary_{timestamp}.json")
    with open(summary_file, "w", encoding="utf-8") as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    logging.info(f"仿真摘要已保存到: {summary_file}")
    return summary_file


def main():
    """主函数 - 增强版（包含数据保存和日志功能）"""
    # 设置日志和输出目录
    logger, output_dir, timestamp = setup_logging()

    print("=" * 60)
    print("非正六边形卷针薄膜包覆动态仿真 - 增强版")
    print("=" * 60)
    logger.info("开始仿真参数设置")

    # 圆角设置
    use_rounded_corners = False
    sharp_radius = 0.8
    blunt_radius = 12.0

    choice = input("\n是否使用圆角仿真? (y/n): ").lower()
    if choice == "y":
        use_rounded_corners = True
        print(f"默认圆角半径 - 锐角: {sharp_radius}mm, 钝角: {blunt_radius}mm")
        logger.info(f"启用圆角仿真 - 锐角: {sharp_radius}mm, 钝角: {blunt_radius}mm")

        modify_choice = input("是否修改圆角半径? (y/n): ").lower()
        if modify_choice == "y":
            try:
                new_sharp = float(input(f"输入锐角圆角半径 (当前: {sharp_radius}mm): "))
                new_blunt = float(input(f"输入钝角圆角半径 (当前: {blunt_radius}mm): "))
                sharp_radius = new_sharp
                blunt_radius = new_blunt
                logger.info(
                    f"圆角半径已修改 - 锐角: {sharp_radius}mm, 钝角: {blunt_radius}mm"
                )
            except ValueError:
                print("输入无效，使用默认值")
                logger.warning("圆角半径输入无效，使用默认值")

        print(f"圆角设置: 锐角半径={sharp_radius}mm, 钝角半径={blunt_radius}mm")
    else:
        print("使用尖角仿真")
        logger.info("使用尖角仿真")

    # 创建仿真对象 (默认X偏移=2.0mm, 保持原有参数)
    rotation_center_x_offset = 2.0
    sim = HexagonSimulation(
        rotation_center_x_offset=rotation_center_x_offset,
        use_rounded_corners=use_rounded_corners,
        sharp_radius=sharp_radius,
        blunt_radius=blunt_radius,
    )

    print("\n🎯 仿真参数:")
    print(f"过辊位置A: ({sim.A[0]:.1f}, {sim.A[1]:.1f})")
    print(f"几何中心: ({sim.geometric_center[0]:.1f}, {sim.geometric_center[1]:.1f})")
    print(f"旋转中心X偏移: {sim.rotation_center_x_offset:.1f} mm")
    print(f"旋转中心位置: ({sim.rotation_center[0]:.1f}, {sim.rotation_center[1]:.1f})")
    print(f"上方特殊点位: ({sim.upper_point[0]:.1f}, {sim.upper_point[1]:.1f})")
    print(f"下方特殊点位: ({sim.lower_point[0]:.1f}, {sim.lower_point[1]:.1f})")
    print(f"圆角模式: {'启用' if sim.use_rounded_corners else '禁用'}")
    if sim.use_rounded_corners:
        print(f"  锐角圆角半径: {sim.sharp_radius}mm")
        print(f"  钝角圆角半径: {sim.blunt_radius}mm")

    # 记录仿真参数到日志
    logger.info("仿真参数设置完成:")
    logger.info(f"  过辊位置A: ({sim.A[0]:.1f}, {sim.A[1]:.1f})")
    logger.info(
        f"  几何中心: ({sim.geometric_center[0]:.1f}, {sim.geometric_center[1]:.1f})"
    )
    logger.info(f"  旋转中心X偏移: {sim.rotation_center_x_offset:.1f} mm")
    logger.info(
        f"  旋转中心位置: ({sim.rotation_center[0]:.1f}, {sim.rotation_center[1]:.1f})"
    )
    logger.info(f"  圆角模式: {'启用' if sim.use_rounded_corners else '禁用'}")
    if sim.use_rounded_corners:
        logger.info(f"    锐角圆角半径: {sim.sharp_radius}mm")
        logger.info(f"    钝角圆角半径: {sim.blunt_radius}mm")

    # 计算初始连接长度
    initial_connection_length = np.linalg.norm(sim.A - sim.upper_point)
    print(f"初始连接：A点到上方点距离: {initial_connection_length:.2f}")
    logger.info(f"初始连接长度: {initial_connection_length:.2f}mm")

    # 可选：修改旋转中心偏移
    choice = input("\n是否修改旋转中心X偏移? (y/n): ").lower()
    if choice == "y":
        try:
            new_offset = float(
                input(f"输入新的X偏移 (当前: {sim.rotation_center_x_offset:.1f} mm): ")
            )
            old_offset = sim.rotation_center_x_offset
            sim.rotation_center_x_offset = new_offset
            sim.rotation_center = np.array(
                [sim.geometric_center[0] + new_offset, sim.geometric_center[1]]
            )
            sim.upper_point = np.array([sim.geometric_center[0] + new_offset, 4])
            sim.lower_point = np.array([sim.geometric_center[0] + new_offset, -4])
            print(f"旋转中心X偏移已设置为: {new_offset:.2f} mm")
            print(
                f"旋转中心位置: ({sim.rotation_center[0]:.2f}, {sim.rotation_center[1]:.2f})"
            )
            logger.info(f"旋转中心X偏移已修改: {old_offset:.2f} -> {new_offset:.2f} mm")
        except ValueError:
            print("输入无效，保持原有偏移值")
            logger.warning("旋转中心偏移输入无效，保持原有值")

    # 保存配置参数
    config = {
        "timestamp": timestamp,
        "use_rounded_corners": use_rounded_corners,
        "sharp_radius": sharp_radius,
        "blunt_radius": blunt_radius,
        "rotation_center_x_offset": sim.rotation_center_x_offset,
        "film_thickness": sim.film_thickness,
        "total_rotation": sim.total_rotation,
        "step_angle": sim.step_angle,
        "geometric_center": sim.geometric_center,
        "rotation_center": sim.rotation_center,
        "roller_A": sim.A,
        "roller_B": sim.B,
        "roller_radius": sim.roller_radius,
        "upper_point": sim.upper_point,
        "lower_point": sim.lower_point,
        "original_vertices": sim.original_vertices,
    }

    _ = save_simulation_config(config, output_dir, timestamp)

    # 可选：查看接触点分析
    choice = input("\n是否查看接触点分析? (y/n): ").lower()
    if choice == "y":
        logger.info("开始接触点分析")
        test_angles = [0, 60, 120, 180]
        for angle in test_angles:
            logger.info(f"分析角度: {angle}°")
            show_contact_analysis(sim, angle)
            if angle < test_angles[-1]:  # 不是最后一个
                input("按回车继续下一个角度...")

    # 运行完整仿真
    print("\n🚀 运行完整仿真...")
    logger.info("开始运行完整仿真")
    logger.info(
        f"总角度: {sim.total_rotation}°, 步长: {sim.step_angle}°, 数据点: {len(sim.theta)}"
    )

    sim.run_simulation()

    logger.info("仿真计算完成")

    # 显示结果统计
    print("\n📊 仿真结果:")
    print(
        f"过辊到接触点长度 - 最大: {np.max(sim.L):.2f}, 最小: {np.min(sim.L):.2f}, 变化: {np.max(sim.L) - np.min(sim.L):.2f}"
    )
    print(f"总薄膜长度: {sim.S_total[-1]:.2f}")
    print(f"  其中切线部分: {sim.roller_contact_distances[-1]:.2f}")
    print(f"  其中包覆部分: {sim.S[-1]:.2f}")

    # 记录结果到日志
    logger.info("仿真结果统计:")
    logger.info(
        f"  过辊到接触点长度 - 最大: {np.max(sim.L):.2f}mm, 最小: {np.min(sim.L):.2f}mm, 变化: {np.max(sim.L) - np.min(sim.L):.2f}mm"
    )
    logger.info(f"  总薄膜长度: {sim.S_total[-1]:.2f}mm")
    logger.info(f"  切线部分: {sim.roller_contact_distances[-1]:.2f}mm")
    logger.info(f"  包覆部分: {sim.S[-1]:.2f}mm")
    logger.info(f"  最终层数: {sim.layer_numbers[-1]}")
    logger.info(f"  最大累积厚度: {sim.accumulated_thickness[-1]:.3f}mm")

    # 保存仿真数据
    print("\n💾 保存仿真数据...")
    logger.info("开始保存仿真数据")

    try:
        # 保存CSV数据
        main_csv, geometry_csv = save_simulation_data_to_csv(sim, output_dir, timestamp)

        # 保存仿真摘要
        summary_file = save_simulation_summary(sim, output_dir, timestamp, config)

        print("✅ 数据保存完成!")
        print(f"   主要数据: {os.path.basename(main_csv)}")
        print(f"   几何数据: {os.path.basename(geometry_csv)}")
        print(f"   仿真摘要: {os.path.basename(summary_file)}")
        print(f"   配置文件: simulation_config_{timestamp}.json")
        print(f"   日志文件: simulation_log_{timestamp}.log")
        print(f"   输出目录: {output_dir}")

        logger.info("所有数据文件保存完成")

    except Exception as e:
        print(f"❌ 数据保存失败: {e}")
        logger.error(f"数据保存失败: {e}")

    # 询问是否保存图表
    save_png = input("\n是否保存结果图表为PNG? (y/n): ").lower() == "y"

    # 创建静态图
    if save_png:
        plot_path = os.path.join(output_dir, f"simulation_results_{timestamp}.png")
        sim.plot_results(save_path=plot_path)
        logger.info(f"结果图表已保存: {plot_path}")
    else:
        sim.plot_results()

    # 询问是否创建动画
    choice = input("\n🎬 是否创建动画? (y/n): ").lower()
    if choice == "y":
        save_gif = input("是否保存为GIF? (y/n): ").lower() == "y"
        if save_gif:
            gif_path = os.path.join(output_dir, f"simulation_animation_{timestamp}.gif")
            sim.create_animation(save_gif=save_gif, gif_path=gif_path)
            logger.info(f"动画已保存: {gif_path}")
        else:
            sim.create_animation(save_gif=save_gif)

    # 仿真完成
    logger.info("=" * 60)
    logger.info("六边形卷针隔膜包覆仿真完成")
    logger.info("=" * 60)
    print(f"\n🎉 仿真完成! 所有文件已保存到: {output_dir}")


def show_contact_analysis(sim, angle_deg):
    """显示指定角度的接触点分析"""
    print(f"\n📍 角度 {angle_deg}° 接触点分析:")

    # 计算厚度
    layer_num, accum_thickness = sim.calculate_layer_from_angle(angle_deg)

    # 更新顶点
    if accum_thickness > 0:
        vertices = sim.thickness_calc.update_vertices_physical(accum_thickness)
    else:
        vertices = sim.original_vertices.copy()

    # 旋转顶点
    theta = np.deg2rad(angle_deg)
    rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
        theta, vertices
    )

    # 旋转上方点
    rotated_upper = sim.rotate_vertices_around_center_with_custom_vertices(
        theta, sim.upper_point.reshape(1, -1)
    )[0]

    # 计算接触点
    contact, contact_type = sim.contact_calc.find_leftmost_valid_contact_point(
        rotated_vertices, rotated_upper, angle_deg
    )

    print(f"  接触点: ({contact[0]:.2f}, {contact[1]:.2f})")
    print(f"  接触类型: {contact_type}")
    print(f"  层数: {layer_num}, 厚度: {accum_thickness:.3f}mm")

    # 计算过辊接触点
    roller_contact = sim.contact_calc.find_roller_contact_point(contact)
    distance = np.linalg.norm(roller_contact - contact)
    print(f"  过辊接触点: ({roller_contact[0]:.2f}, {roller_contact[1]:.2f})")
    print(f"  切线长度: {distance:.2f}mm")


def show_geometry_snapshot(sim, angle_deg):
    """显示指定角度的几何快照"""
    # 找到最接近的数据点
    idx = np.argmin(np.abs(sim.theta_deg - angle_deg))
    actual_angle = sim.theta_deg[idx]

    print(f"显示角度 {actual_angle:.1f}° 的几何快照...")

    # 获取该角度的数据
    _, accum_thickness = sim.calculate_layer_from_angle(actual_angle)

    # 计算顶点位置
    if accum_thickness > 0:
        current_vertices = sim.thickness_calc.update_vertices_physical(accum_thickness)
    else:
        current_vertices = sim.original_vertices.copy()

    # 旋转顶点
    rotated_vertices = sim.rotate_vertices_around_center_with_custom_vertices(
        sim.theta[idx], current_vertices
    )

    # 获取接触点
    contact_point = sim.contact_points[idx]

    # 绘制几何快照
    sim.visualizer.plot_geometry_snapshot(
        rotated_vertices,
        contact_point,
        sim.roller_A,
        sim.roller_radius,
        actual_angle,
        rounded_corner_calc=sim.rounded_corner_calc,
    )


def quick_test():
    """快速测试函数"""
    print("运行快速测试...")

    # 创建小规模测试
    sim = HexagonSimulation(
        rotation_center_x_offset=2.0,
        film_thickness=0.1,
        total_rotation=720,  # 只测试2圈
        step_angle=5.0,  # 较大步长
    )

    # 运行仿真
    sim.run_simulation()

    # 显示结果
    sim.plot_results()

    return sim


if __name__ == "__main__":
    # 检查是否为快速测试模式
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        quick_test()
    else:
        main()
